import { ipcMain } from 'electron'
import { tabs, settings, window as windowModule } from '../database'
import type { ApiResponse, Tab, AppSettings, WindowState } from '../../shared/types'
import { createApiResponse } from '../../shared/types'

export function setupDatabaseIPC(): void {
  // 保存标签页
  ipcMain.handle('db:save-tabs', async (_, tabsData: Tab[]): Promise<ApiResponse> => {
    try {
      // 直接传递标签页数据，repository 层会处理转换
      await tabs.save(tabsData)
      return createApiResponse(true)
    } catch (error) {
      console.error('Failed to save tabs:', error)
      return createApiResponse(
        false,
        undefined,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 获取标签页
  ipcMain.handle('db:get-tabs', async (): Promise<ApiResponse<Tab[]>> => {
    try {
      // 直接获取标签页数据，repository 层已经处理了转换
      const tabsData = await tabs.getAll()
      return createApiResponse(true, tabsData)
    } catch (error) {
      console.error('Failed to get tabs:', error)
      return createApiResponse(
        false,
        [] as Tab[],
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 保存设置
  ipcMain.handle('db:save-setting', async (_, key: string, value: any): Promise<ApiResponse> => {
    try {
      await settings.save(key, value)
      return createApiResponse(true)
    } catch (error) {
      console.error('Failed to save setting:', error)
      return createApiResponse(
        false,
        undefined,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 获取设置
  ipcMain.handle(
    'db:get-setting',
    async (_, key: string, defaultValue?: any): Promise<ApiResponse<any>> => {
      try {
        const value = await settings.get(key, defaultValue)
        return createApiResponse(true, value)
      } catch (error) {
        console.error('Failed to get setting:', error)
        return createApiResponse(
          false,
          undefined,
          error instanceof Error ? error.message : 'Unknown error'
        )
      }
    }
  )

  // 获取所有设置
  ipcMain.handle('db:get-all-settings', async (): Promise<ApiResponse<AppSettings>> => {
    try {
      const settingsData = await settings.getAll()
      return createApiResponse(true, settingsData as AppSettings)
    } catch (error) {
      console.error('Failed to get all settings:', error)
      return createApiResponse(
        false,
        {} as AppSettings,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 保存窗口状态
  ipcMain.handle('db:save-window-state', async (_, state: WindowState): Promise<ApiResponse> => {
    try {
      // 直接传递窗口状态，repository 层会处理转换
      await windowModule.save(state)
      return createApiResponse(true)
    } catch (error) {
      console.error('Failed to save window state:', error)
      return createApiResponse(
        false,
        undefined,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 获取窗口状态
  ipcMain.handle('db:get-window-state', async (): Promise<ApiResponse<WindowState | null>> => {
    try {
      // 直接获取窗口状态，repository 层已经处理了转换
      const state = await windowModule.get()
      return createApiResponse(true, state)
    } catch (error) {
      console.error('Failed to get window state:', error)
      return createApiResponse(
        false,
        null,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 数据库清理
  ipcMain.handle('db:cleanup', async (): Promise<ApiResponse> => {
    try {
      await tabs.cleanup(30)
      await settings.cleanup()
      return createApiResponse(true)
    } catch (error) {
      console.error('Failed to cleanup database:', error)
      return createApiResponse(
        false,
        undefined,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })
}
