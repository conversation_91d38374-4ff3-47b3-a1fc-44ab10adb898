import type { DatabaseTab } from '../../../shared/types'
import { TabRepository } from '../repositories/tab-repository'

export class TabService {
  private tabRepository: TabRepository

  constructor(tabRepository: TabRepository) {
    this.tabRepository = tabRepository
  }

  /**
   * 保存标签页数据
   */
  async saveTabs(tabs: DatabaseTab[]): Promise<void> {
    // 验证标签页数据
    this.validateTabs(tabs)

    // 确保每个标签页都有必要的时间戳
    const processedTabs = tabs.map((tab) => ({
      ...tab,
      createdAt: tab.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }))

    await this.tabRepository.saveTabs(processedTabs)
  }

  /**
   * 获取所有标签页
   */
  async getTabs(): Promise<DatabaseTab[]> {
    return await this.tabRepository.getTabs()
  }

  /**
   * 根据ID获取标签页
   */
  async getTabById(id: string): Promise<DatabaseTab | undefined> {
    if (!id) {
      throw new Error('Tab ID is required')
    }
    return await this.tabRepository.getTabById(id)
  }

  /**
   * 更新标签页
   */
  async updateTab(id: string, updates: Partial<DatabaseTab>): Promise<void> {
    if (!id) {
      throw new Error('Tab ID is required')
    }

    // 确保更新时间戳
    const processedUpdates = {
      ...updates,
      updatedAt: new Date().toISOString()
    }

    await this.tabRepository.updateTab(id, processedUpdates)
  }

  /**
   * 删除标签页
   */
  async deleteTab(id: string): Promise<void> {
    if (!id) {
      throw new Error('Tab ID is required')
    }

    // 检查是否为首页标签页
    const tab = await this.tabRepository.getTabById(id)
    if (tab && tab.type === 'home') {
      throw new Error('Cannot delete home tab')
    }

    await this.tabRepository.deleteTab(id)
  }

  /**
   * 获取活跃标签页
   */
  async getActiveTab(): Promise<DatabaseTab | undefined> {
    const tabs = await this.tabRepository.getTabs()
    return tabs.find((tab) => tab.active)
  }

  /**
   * 设置活跃标签页
   */
  async setActiveTab(id: string): Promise<void> {
    const tabs = await this.tabRepository.getTabs()

    // 取消所有标签页的活跃状态
    for (const tab of tabs) {
      if (tab.active) {
        await this.tabRepository.updateTab(tab.id, { active: false })
      }
    }

    // 设置指定标签页为活跃
    await this.tabRepository.updateTab(id, { active: true })
  }

  /**
   * 清理旧的标签页记录
   */
  async cleanupOldTabs(daysToKeep: number = 30): Promise<void> {
    await this.tabRepository.cleanupOldTabs(daysToKeep)
  }

  /**
   * 验证标签页数据
   */
  private validateTabs(tabs: DatabaseTab[]): void {
    for (const tab of tabs) {
      if (!tab.id) {
        throw new Error('Tab ID is required')
      }
      if (!tab.title) {
        throw new Error('Tab title is required')
      }
      if (!tab.type) {
        throw new Error('Tab type is required')
      }
    }

    // 确保只有一个活跃标签页
    const activeTabs = tabs.filter((tab) => tab.active)
    if (activeTabs.length > 1) {
      throw new Error('Only one tab can be active at a time')
    }
  }

  /**
   * 获取标签页统计信息
   */
  async getTabStats(): Promise<{
    total: number
    active: number
    pinned: number
    dirty: number
  }> {
    const tabs = await this.tabRepository.getTabs()

    return {
      total: tabs.length,
      active: tabs.filter((tab) => tab.active).length,
      pinned: tabs.filter((tab) => tab.isPinned).length,
      dirty: tabs.filter((tab) => tab.isDirty).length
    }
  }
}
